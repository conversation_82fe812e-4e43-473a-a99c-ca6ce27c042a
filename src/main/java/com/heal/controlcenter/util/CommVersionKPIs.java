package com.heal.controlcenter.util;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> : 4/2/19
 */
@Data
public class CommVersionKPIs {
    private int mstCommonVersionId;
    private int accountId;
    private int defaultAccountId = Constants.DEFAULT_ACCOUNT_ID;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CommVersionKPIs that = (CommVersionKPIs) o;
        return mstCommonVersionId == that.mstCommonVersionId &&
                accountId == that.accountId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(mstCommonVersionId, accountId);
    }
}
