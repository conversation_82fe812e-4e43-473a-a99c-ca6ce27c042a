package com.heal.controlcenter.dao.mysql.entity;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.heal.configuration.entities.InstanceMetadataBean;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.PaginationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;

@Slf4j
@Repository
public class ComponentInstanceDao {

    private final JdbcTemplate jdbcTemplate;

    /**
     * Constructor for dependency injection.
     * @param jdbcTemplate Spring JDBC template for database operations
     */
    public ComponentInstanceDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<ViewComponentInstanceBean> getActiveInstanceDetailsForAccount(int accountId) {
        String query = "SELECT id, name, identifier, host_id AS hostId, host_name AS hostName, is_cluster AS isCluster, " +
                "discovery, host_address AS hostAddress, is_DR AS isDR, " +
                "mst_component_id AS mstComponentId, component_name AS mstComponentName, " +
                "mst_component_type_id AS mstComponentTypeId, component_type_name AS mstComponentTypeName, " +
                "mst_component_version_id AS mstComponentVersionId, component_version_name AS componentVersionName, " +
                "common_version_id AS commonVersionId, common_version_name AS commonVersionName, " +
                "updated_time AS updatedTime " +
                "FROM view_component_instance vci " +
                "WHERE account_id = ? AND status = 1 AND is_cluster = 0";

        try {
            log.debug("Fetching active instance details for accountId: {}", accountId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewComponentInstanceBean.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching active instance details for accountId: {}. Details:", accountId, e);
            return Collections.emptyList();
        }
    }

    public List<ViewClusterServicesBean> getAllClusterServices() {
        String query = "SELECT id AS clusterId, name AS clusterName, identifier AS clusterIdentifier, " +
                "host_cluster_id AS hostClusterId, mst_component_id AS mstComponentId, " +
                "mst_component_type_id AS mstComponentTypeId, mst_component_version_id AS mstComponentVersionId, " +
                "service_id AS serviceId, service_name AS serviceName, service_identifier AS serviceIdentifier " +
                "FROM view_cluster_services";

        try {
            log.debug("Fetching all cluster services");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewClusterServicesBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching cluster services. Details:", e);
            return Collections.emptyList();
        }
    }

    public List<ViewApplicationServiceMappingBean> getAllServiceApplication(int accountId) {
        String query = "SELECT application_id AS applicationId, " +
                "application_name AS applicationName, " +
                "application_identifier AS applicationIdentifier, " +
                "service_id AS serviceId, " +
                "service_name AS serviceName, " +
                "service_identifier AS serviceIdentifier, " +
                "account_id AS accountId " +
                "FROM view_application_service_mapping " +
                "WHERE account_id = ?";

        try {
            log.debug("Fetching all service-application mappings for accountId: {}", accountId);
            return jdbcTemplate.query(query,
                    new BeanPropertyRowMapper<>(ViewApplicationServiceMappingBean.class),
                    accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching service-application mappings for accountId: {}. Details:", accountId, e);
            return Collections.emptyList();
        }
    }

    public List<CompInstanceAttributesBean> getInstAttributeMapping(String attributeName) {
        String query = "SELECT ciav.id, ciav.attribute_value AS attributeValue, " +
                "ciav.comp_instance_id AS compInstanceId, " +
                "ciav.mst_component_attribute_mapping_id AS mstComponentAttributeMappingId, " +
                "ciav.mst_common_attributes_id AS mstCommonAttributesId, " +
                "ciav.attribute_name AS attributeName " +
                "FROM comp_instance_attribute_values ciav, mst_common_attributes mca " +
                "WHERE ciav.mst_common_attributes_id = mca.id AND mca.attribute_name = ?";

        try {
            log.debug("Fetching instance attribute mapping for attributeName: {}", attributeName);
            return jdbcTemplate.query(query,
                    new BeanPropertyRowMapper<>(CompInstanceAttributesBean.class),
                    attributeName);
        } catch (Exception e) {
            log.error("Error occurred while fetching instance attribute mappings for attributeName: {}. Details:", attributeName, e);
            return Collections.emptyList();
        }
    }

    /**
     * Retrieves paginated component instances for the specified account with optional search filtering.
     * @param accountId The account ID for which to fetch instances
     * @param searchTerm Optional search term to filter instances
     * @param pageable Pagination parameters
     * @return Paginated list of component instances for the specified account
     * @throws HealControlCenterException if database operations fail
     */
    public List<GetCompInstance> getComponentInstancesForAccount(int accountId, String searchTerm, int hostComponentTypeId, Pageable pageable) throws HealControlCenterException {
        String baseQuery = "SELECT id, name, identifier, host_id, host_name, host_identifier, " +
                "host_address, mst_component_id, component_name, mst_component_type_id, " +
                "component_type_name, mst_component_version_id, component_version_name, " +
                "common_version_id, common_version_name, status, discovery, is_DR, " +
                "updated_time FROM view_component_instance WHERE account_id = ? AND status = 1 AND is_cluster = 0 AND mst_component_type_id != ?";

        List<Object> params = new ArrayList<>();
        params.add(accountId);
        params.add(hostComponentTypeId);

        // Add search filter if provided
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(name) LIKE ? OR LOWER(component_name) LIKE ? OR LOWER(host_name) LIKE ?)";
            String searchPattern = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(searchPattern);
            params.add(searchPattern);
            params.add(searchPattern);
        }

        // Apply pagination
        String paginatedQuery = PaginationUtils.applyPagination(baseQuery, pageable);
        params = PaginationUtils.buildPaginationParams(params, pageable);

        try {
            log.debug("Fetching paginated component instances for accountId: {} with searchTerm: {}", accountId, searchTerm);
            return jdbcTemplate.query(paginatedQuery, new GetCompInstanceRowMapper(), params.toArray());
        } catch (Exception e) {
            log.error("Error occurred while fetching paginated component instances for accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while fetching paginated component instances.");
        }
    }

    /**
     * Counts total component instances for the specified account with optional search filtering.
     * @param accountId The account ID for which to count instances
     * @param searchTerm Optional search term to filter instances
     * @return Total count of component instances
     * @throws HealControlCenterException if database operations fail
     */
    public int countComponentInstancesForAccount(int accountId, String searchTerm, int hostComponentTypeId) throws HealControlCenterException {
        String baseQuery = "SELECT COUNT(*) FROM view_component_instance WHERE account_id = ? AND status = 1 AND is_cluster = 0 AND mst_component_type_id != ?";

        List<Object> params = new ArrayList<>();
        params.add(accountId);
        params.add(hostComponentTypeId);

        // Add search filter if provided
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(name) LIKE ? OR LOWER(component_name) LIKE ? OR LOWER(host_name) LIKE ?)";
            String searchPattern = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(searchPattern);
            params.add(searchPattern);
            params.add(searchPattern);
        }

        try {
            log.debug("Counting component instances for accountId: {} with searchTerm: {}", accountId, searchTerm);
            return jdbcTemplate.queryForObject(baseQuery, Integer.class, params.toArray());
        } catch (Exception e) {
            log.error("Error occurred while counting component instances for accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while counting component instances.");
        }
    }

    /**
     * Retrieves component instance by identifier from comp_instance table.
     *
     * @param identifier the component instance identifier
     * @param accountId the account ID
     * @return ComponentInstanceBean containing component instance details, or null if not found
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public ComponentInstanceBean getComponentInstanceByIdentifier(String identifier, int accountId) throws HealControlCenterException {
        String query = "SELECT id, name, status, host_id AS hostId, is_dr AS isDR, is_cluster AS isCluster, " +
                "mst_component_version_id AS mstComponentVersionId, created_time AS createdTime, updated_time AS updatedTime, " +
                "user_details_id AS userDetailsId, account_id AS accountId, mst_component_id AS mstComponentId, " +
                "mst_component_type_id AS mstComponentTypeId, discovery, host_address AS hostAddress, identifier, " +
                "mst_common_version_id AS mstCommonVersionId, supervisor_id AS supervisorId " +
                "FROM comp_instance WHERE identifier = ? AND account_id = ?";

        try {
            log.debug("Querying comp_instance for identifier: {} and accountId: {}", identifier, accountId);
            return jdbcTemplate.queryForObject(query, (rs, rowNum) -> {
                ComponentInstanceBean componentInstance = new ComponentInstanceBean();
                componentInstance.setId(rs.getInt("id"));
                componentInstance.setName(rs.getString("name"));
                componentInstance.setStatus(rs.getInt("status"));
                componentInstance.setHostId(rs.getInt("hostId"));
                componentInstance.setIsDR(rs.getInt("isDR"));
                componentInstance.setIsCluster(rs.getInt("isCluster"));
                componentInstance.setMstComponentVersionId(rs.getInt("mstComponentVersionId"));
                componentInstance.setCreatedTime(rs.getString("createdTime"));
                componentInstance.setUpdatedTime(rs.getString("updatedTime"));
                componentInstance.setUserDetailsId(rs.getString("userDetailsId"));
                componentInstance.setAccountId(rs.getInt("accountId"));
                componentInstance.setMstComponentId(rs.getInt("mstComponentId"));
                componentInstance.setMstComponentTypeId(rs.getInt("mstComponentTypeId"));
                componentInstance.setDiscovery(rs.getInt("discovery"));
                componentInstance.setHostAddress(rs.getString("hostAddress"));
                componentInstance.setIdentifier(rs.getString("identifier"));
                componentInstance.setMstCommonVersionId(rs.getInt("mstCommonVersionId"));
                componentInstance.setSupervisorId(rs.getInt("supervisorId"));

                return componentInstance;
            }, identifier, accountId);
        } catch (EmptyResultDataAccessException e) {
            log.debug("No component instance found for identifier: {} and accountId: {}", identifier, accountId);
            return null; // Return null when no record is found
        } catch (Exception e) {
            log.error("Error occurred while fetching component instance by identifier [{}] and accountId [{}]. Details: ",
                    identifier, accountId, e);
            throw new HealControlCenterException("Error occurred while fetching component instance by identifier.");
        }
    }

    /**
     * Retrieves component instance tag mapping details by joining tag_mapping and component_cluster_mapping tables.
     *
     * @param instanceId the component instance ID
     * @param tagId the tag ID
     * @param accountId the account ID
     * @return TagMappingBean containing tag mapping details, or null if not found
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public TagMappingBean getComponentInstanceTagMappingDetails(int instanceId, int tagId, int accountId) throws HealControlCenterException {
        String query = "SELECT tm.id, tm.tag_id AS tagId, tm.object_id AS objectId, tm.object_ref_table AS objectRefTable, " +
                "tm.tag_key AS tagKey, tm.tag_value AS tagValue, tm.account_id AS accountId, tm.user_details_id AS userDetailsId " +
                "FROM tag_mapping tm " +
                "JOIN component_cluster_mapping cm ON tm.object_id = cm.cluster_id AND tm.account_id = cm.account_id " +
                "WHERE tm.account_id = ? AND cm.comp_instance_id = ? AND tm.tag_id = ? AND tm.object_ref_table = 'comp_instance'";

        try {
            log.debug("Querying tag_mapping with component_cluster_mapping for instanceId: {}, tagId: {}, accountId: {}",
                    instanceId, tagId, accountId);
            return jdbcTemplate.queryForObject(query, (rs, rowNum) -> {
                TagMappingBean tagMapping = new TagMappingBean();
                tagMapping.setId(rs.getInt("id"));
                tagMapping.setTagId(rs.getInt("tagId"));
                tagMapping.setObjectId(rs.getInt("objectId"));
                tagMapping.setObjectRefTable(rs.getString("objectRefTable"));
                tagMapping.setTagKey(rs.getString("tagKey"));
                tagMapping.setTagValue(rs.getString("tagValue"));
                tagMapping.setAccountId(rs.getInt("accountId"));
                tagMapping.setUserDetailsId(rs.getString("userDetailsId"));

                return tagMapping;
            }, accountId, instanceId, tagId);
        } catch (Exception e) {
            log.error("Error occurred while fetching component instance tag mapping details for instanceId [{}], tagId [{}], accountId [{}]. Details: ",
                    instanceId, tagId, accountId, e);
            throw new HealControlCenterException("Error occurred while fetching component instance tag mapping details.");
        }
    }

    /**
     * Retrieves active component instance by identifier or name from comp_instance table.
     *
     * @param identifier the component instance identifier
     * @param name the component instance name
     * @param accountId the account ID
     * @return ComponentInstanceBean containing component instance details, or null if not found
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public ComponentInstanceBean getActiveComponentInstance(String identifier, String name, int accountId) throws HealControlCenterException {
        String query = "SELECT id, name, status, host_id AS hostId, is_dr AS isDR, is_cluster AS isCluster, " +
                "mst_component_version_id AS mstComponentVersionId, created_time AS createdTime, updated_time AS updatedTime, " +
                "user_details_id AS userDetailsId, account_id AS accountId, mst_component_id AS mstComponentId, " +
                "mst_component_type_id AS mstComponentTypeId, discovery, host_address AS hostAddress, identifier, " +
                "mst_common_version_id AS mstCommonVersionId, supervisor_id AS supervisorId " +
                "FROM comp_instance WHERE account_id = ? AND status = 1 AND (identifier = ? OR name = ?)";

        try {
            log.debug("Querying active comp_instance for identifier: {}, name: {}, accountId: {}", identifier, name, accountId);
            return jdbcTemplate.queryForObject(query, (rs, rowNum) -> {
                ComponentInstanceBean componentInstance = new ComponentInstanceBean();
                componentInstance.setId(rs.getInt("id"));
                componentInstance.setName(rs.getString("name"));
                componentInstance.setStatus(rs.getInt("status"));
                componentInstance.setHostId(rs.getInt("hostId"));
                componentInstance.setIsDR(rs.getInt("isDR"));
                componentInstance.setIsCluster(rs.getInt("isCluster"));
                componentInstance.setMstComponentVersionId(rs.getInt("mstComponentVersionId"));
                componentInstance.setCreatedTime(rs.getString("createdTime"));
                componentInstance.setUpdatedTime(rs.getString("updatedTime"));
                componentInstance.setUserDetailsId(rs.getString("userDetailsId"));
                componentInstance.setAccountId(rs.getInt("accountId"));
                componentInstance.setMstComponentId(rs.getInt("mstComponentId"));
                componentInstance.setMstComponentTypeId(rs.getInt("mstComponentTypeId"));
                componentInstance.setDiscovery(rs.getInt("discovery"));
                componentInstance.setHostAddress(rs.getString("hostAddress"));
                componentInstance.setIdentifier(rs.getString("identifier"));
                componentInstance.setMstCommonVersionId(rs.getInt("mstCommonVersionId"));
                componentInstance.setSupervisorId(rs.getInt("supervisorId"));

                return componentInstance;
            }, accountId, identifier, name);
        } catch (Exception e) {
            log.error("Error occurred while fetching active component instance by identifier [{}] or name [{}] and accountId [{}]. Details: ",
                    identifier, name, accountId, e);
            throw new HealControlCenterException("Error occurred while fetching active component instance.");
        }
    }

    /**
     * Retrieves component instance ID by attribute, service, and cluster using multiple table JOINs.
     * This method joins comp_instance, tag_mapping, component_cluster_mapping, and comp_instance_attribute_values tables.
     *
     * @param componentId the master component type ID
     * @param attributeName the attribute name to search for
     * @param attributeValue the attribute value to match
     * @param serviceId the service ID (used as tag_key)
     * @param tagId the tag ID
     * @param accountId the account ID
     * @return the component instance ID, or 0 if not found
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public int getComponentInstanceIdByAttributeServiceCluster(int componentId, String attributeName, String attributeValue, int serviceId, int tagId, int accountId) throws HealControlCenterException {
        String query = "SELECT ci.id FROM comp_instance ci " +
                "JOIN tag_mapping tm ON ci.id = tm.object_id " +
                "WHERE ci.account_id = ? AND ci.mst_component_type_id = ? AND ci.is_cluster = 1 " +
                "AND tm.tag_id = ? AND tm.tag_key = ? AND tm.object_ref_table = 'comp_instance' " +
                "AND tm.object_id IN (" +
                "    SELECT cm.cluster_id FROM component_cluster_mapping cm " +
                "    JOIN comp_instance_attribute_values ca ON ca.comp_instance_id = cm.comp_instance_id " +
                "    WHERE ca.attribute_name = ? AND ca.attribute_value = ?" +
                ")";

        try {
            log.debug("Querying component instance ID with joins for componentId: {}, attributeName: {}, attributeValue: {}, serviceId: {}, tagId: {}, accountId: {}",
                    componentId, attributeName, attributeValue, serviceId, tagId, accountId);
            return jdbcTemplate.queryForObject(query, Integer.class,
                    accountId, componentId, tagId, serviceId, attributeName, attributeValue);
        } catch (Exception e) {
            log.error("Error occurred while fetching component instance ID for componentId [{}], attributeName [{}], attributeValue [{}], serviceId [{}], tagId [{}], accountId [{}]. Details: ",
                    componentId, attributeName, attributeValue, serviceId, tagId, accountId, e);
            throw new HealControlCenterException("Error occurred while fetching component instance ID by attribute service cluster.");
        }
    }

    /**
     * Retrieves cluster ID by joining comp_instance and tag_mapping tables.
     * This method uses JOIN as it requires data from multiple tables.
     *
     * @param componentId the master component ID
     * @param commonVersionId the master common version ID
     * @param componentTypeId the master component type ID
     * @param serviceId the service ID (used as tag_key)
     * @param tagId the tag ID
     * @param accountId the account ID
     * @return the cluster ID, or 0 if not found
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public int getClusterId(int componentId, int commonVersionId, int componentTypeId, int serviceId, int tagId, int accountId) throws HealControlCenterException {
        String query = "SELECT ci.id FROM comp_instance ci " +
                "JOIN tag_mapping tm ON ci.id = tm.object_id " +
                "WHERE ci.account_id = ? AND ci.is_cluster = 1 AND ci.mst_component_id = ? " +
                "AND ci.mst_common_version_id = ? AND ci.mst_component_type_id = ? " +
                "AND tm.tag_id = ? AND tm.tag_key = ? AND tm.object_ref_table = 'comp_instance'";

        try {
            log.debug("Querying cluster ID with join for componentId: {}, commonVersionId: {}, componentTypeId: {}, serviceId: {}, tagId: {}, accountId: {}",
                    componentId, commonVersionId, componentTypeId, serviceId, tagId, accountId);
            return jdbcTemplate.queryForObject(query, Integer.class,
                    accountId, componentId, commonVersionId, componentTypeId, tagId, serviceId);
        } catch (Exception e) {
            log.error("Error occurred while fetching cluster ID for componentId [{}], commonVersionId [{}], componentTypeId [{}], serviceId [{}], tagId [{}], accountId [{}]. Details: ",
                    componentId, commonVersionId, componentTypeId, serviceId, tagId, accountId, e);
            throw new HealControlCenterException("Error occurred while fetching cluster ID.");
        }
    }

    /**
     * Adds a new component instance to the comp_instance table.
     *
     * @param bean the ComponentInstanceBean containing the instance details
     * @return the generated component instance ID
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public int addComponentInstance(ComponentInstanceBean bean) throws HealControlCenterException {
        String sql = "INSERT INTO comp_instance (name, host_id, is_DR, is_cluster, mst_component_version_id, " +
                "created_time, updated_time, user_details_id, account_id, mst_component_id, mst_component_type_id, " +
                "discovery, host_address, identifier, mst_common_version_id, parent_instance_id, supervisor_id) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try {
            log.debug("Inserting component instance with name: {}, identifier: {}, accountId: {}",
                    bean.getName(), bean.getIdentifier(), bean.getAccountId());

            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, bean.getName());
                ps.setInt(2, bean.getHostId());
                ps.setInt(3, bean.getIsDR());
                ps.setInt(4, bean.getIsCluster());
                ps.setInt(5, bean.getMstComponentVersionId());
                ps.setString(6, bean.getCreatedTime());
                ps.setString(7, bean.getUpdatedTime());
                ps.setString(8, bean.getUserDetailsId());
                ps.setInt(9, bean.getAccountId());
                ps.setInt(10, bean.getMstComponentId());
                ps.setInt(11, bean.getMstComponentTypeId());
                ps.setInt(12, bean.getDiscovery());
                ps.setString(13, bean.getHostAddress());
                ps.setString(14, bean.getIdentifier());
                ps.setInt(15, bean.getMstCommonVersionId());
                ps.setInt(16, bean.getParentId());
                ps.setInt(17, bean.getSupervisorId());
                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            int generatedId = key != null ? key.intValue() : -1;

            log.debug("Successfully inserted component instance with generated ID: {}", generatedId);

            InstanceMetadataBean metadata = InstanceMetadataBean.builder()
                    .id(generatedId)
                    .userDetailsId(bean.getUserDetailsId())
                    .createdTime(bean.getCreatedTime())
                    .updatedTime(bean.getUpdatedTime())
                    .environmentId(383)
                    .status(1)
                    .build();

            int instanceMetadataId = addInstanceMetadata(metadata);
            log.debug("Inserted instance metadata for instanceId:{}, metadataId:{}", generatedId, instanceMetadataId);

            return generatedId;

        } catch (Exception e) {
            log.error("Error occurred while adding component instance with name [{}], identifier [{}], accountId [{}]. Details: ",
                    bean.getName(), bean.getIdentifier(), bean.getAccountId(), e);
            throw new HealControlCenterException("Error occurred while adding component instance.");
        }
    }

    /**
     * Adds instance metadata to the instance_metadata table.
     *
     * @param metadata the InstanceMetadataBean containing the metadata details
     * @return the generated instance metadata ID
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    private int addInstanceMetadata(InstanceMetadataBean metadata) throws HealControlCenterException {
        String sql = "INSERT INTO instance_metadata (instance_id, environment_id, created_time, updated_time, user_details_id, status) " +
                "VALUES (?, ?, ?, ?, ?, ?)";

        try {
            log.debug("Inserting instance metadata for instanceId: {}, environmentId: {}",
                    metadata.getId(), metadata.getEnvironmentId());

            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setInt(1, metadata.getId());
                ps.setInt(2, metadata.getEnvironmentId());
                ps.setString(3, metadata.getCreatedTime());
                ps.setString(4, metadata.getUpdatedTime());
                ps.setString(5, metadata.getUserDetailsId());
                ps.setInt(6, metadata.getStatus());
                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            int generatedId = key != null ? key.intValue() : -1;

            log.debug("Successfully inserted instance metadata with generated ID: {}", generatedId);
            return generatedId;

        } catch (Exception e) {
            log.error("Error occurred while adding instance metadata for instanceId [{}], environmentId [{}]. Details: ",
                    metadata.getId(), metadata.getEnvironmentId(), e);
            throw new HealControlCenterException("Error occurred while adding instance metadata.");
        }
    }

    /**
     * Updates an existing component instance in the comp_instance table.
     *
     * @param bean the ComponentInstanceBean containing the updated instance details
     * @return the number of rows affected by the update operation
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public int updateComponentInstance(ComponentInstanceBean bean) throws HealControlCenterException {
        String sql = "UPDATE comp_instance SET name = ?, host_id = ?, is_DR = ?, is_cluster = ?, " +
                "mst_component_version_id = ?, updated_time = ?, user_details_id = ?, " +
                "mst_component_id = ?, mst_component_type_id = ?, discovery = ?, host_address = ?, " +
                "mst_common_version_id = ?, parent_instance_id = ?, status = ?, supervisor_id = ? " +
                "WHERE identifier = ? AND account_id = ?";

        try {
            log.debug("Updating component instance with identifier: {}, accountId: {}",
                    bean.getIdentifier(), bean.getAccountId());

            int rowsAffected = jdbcTemplate.update(sql,
                    bean.getName(),
                    bean.getHostId(),
                    bean.getIsDR(),
                    bean.getIsCluster(),
                    bean.getMstComponentVersionId(),
                    bean.getUpdatedTime(),
                    bean.getUserDetailsId(),
                    bean.getMstComponentId(),
                    bean.getMstComponentTypeId(),
                    bean.getDiscovery(),
                    bean.getHostAddress(),
                    bean.getMstCommonVersionId(),
                    bean.getParentId(),
                    bean.getStatus(),
                    bean.getSupervisorId(),
                    bean.getIdentifier(),
                    bean.getAccountId()
            );

            log.debug("Successfully updated component instance. Rows affected: {}", rowsAffected);
            return rowsAffected;

        } catch (Exception e) {
            log.error("Error occurred while updating component instance with identifier [{}], accountId [{}]. Details: ",
                    bean.getIdentifier(), bean.getAccountId(), e);
            throw new HealControlCenterException("Error occurred while updating component instance.");
        }
    }

    /**
     * Retrieves default component instance KPI data by joining view_common_version_kpis and view_producer_kpis tables.
     * This method uses JOINs as it requires data from multiple tables.
     *
     * @param componentId the master component ID
     * @param mstCommonVersionId the master common version ID
     * @param mstComponentVersionId the master component version ID
     * @param mstComponentTypeId the master component type ID
     * @return List of CompInstanceKpiDetailsBean containing default KPI data
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public List<CompInstanceKpiDetailsBean> getDefaultCompInstanceKPIsData(int componentId, int mstCommonVersionId, int mstComponentVersionId, int mstComponentTypeId) throws HealControlCenterException {
        String query = "SELECT DISTINCT p.mst_producer_kpi_mapping_id AS mstProducerKpiMappingId, " +
                "c.default_collection_interval AS collectionInterval, p.mst_kpi_details_id AS mstKpiDetailsId, " +
                "p.producer_id AS mstProducerId " +
                "FROM view_common_version_kpis c " +
                "JOIN view_producer_kpis p ON c.mst_component_id = p.mst_component_id AND c.kpi_id = p.mst_kpi_details_id " +
                "WHERE c.mst_component_id = ? AND c.mst_common_version_id = ? " +
                "AND p.mst_component_version_id = ? AND p.mst_component_type_id = ? " +
                "AND c.kpi_group_id = 0 AND c.status = 1 AND p.is_default = 1";

        try {
            log.debug("Querying default component instance KPIs for componentId: {}, commonVersionId: {}, componentVersionId: {}, componentTypeId: {}",
                    componentId, mstCommonVersionId, mstComponentVersionId, mstComponentTypeId);

            return jdbcTemplate.query(query, (rs, rowNum) -> {
                CompInstanceKpiDetailsBean kpiDetails = new CompInstanceKpiDetailsBean();
                kpiDetails.setMstProducerKpiMappingId(rs.getInt("mstProducerKpiMappingId"));
                kpiDetails.setCollectionInterval(rs.getInt("collectionInterval"));
                kpiDetails.setMstKpiDetailsId(rs.getInt("mstKpiDetailsId"));
                kpiDetails.setMstProducerId(rs.getInt("mstProducerId"));
                return kpiDetails;
            }, componentId, mstCommonVersionId, mstComponentVersionId, mstComponentTypeId);

        } catch (Exception e) {
            log.error("Error occurred while fetching default component instance KPIs for componentId [{}], commonVersionId [{}], componentVersionId [{}], componentTypeId [{}]. Details: ",
                    componentId, mstCommonVersionId, mstComponentVersionId, mstComponentTypeId, e);
            throw new HealControlCenterException("Error occurred while fetching default component instance KPIs.");
        }
    }

    /**
     * Adds non-group component instance KPI details to the comp_instance_kpi_details table.
     *
     * @param compInstanceKpiDetailsBean the CompInstanceKpiDetailsBean containing the KPI details
     * @return the generated component instance KPI details ID
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public int addNonGroupCompInstanceKpiDetails(CompInstanceKpiDetailsBean compInstanceKpiDetailsBean) throws HealControlCenterException {
        String sql = "INSERT INTO comp_instance_kpi_details (comp_instance_id, mst_producer_kpi_mapping_id, " +
                "collection_interval, created_time, updated_time, user_details_id, mst_kpi_details_id, mst_producer_id) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

        try {
            log.debug("Inserting non-group component instance KPI details for compInstanceId: {}, kpiId: {}",
                    compInstanceKpiDetailsBean.getCompInstanceId(), compInstanceKpiDetailsBean.getMstKpiDetailsId());

            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setInt(1, compInstanceKpiDetailsBean.getCompInstanceId());
                ps.setInt(2, compInstanceKpiDetailsBean.getMstProducerKpiMappingId());
                ps.setInt(3, compInstanceKpiDetailsBean.getCollectionInterval());
                ps.setString(4, compInstanceKpiDetailsBean.getCreatedTime());
                ps.setString(5, compInstanceKpiDetailsBean.getUpdatedTime());
                ps.setString(6, compInstanceKpiDetailsBean.getUserDetailsId());
                ps.setInt(7, compInstanceKpiDetailsBean.getMstKpiDetailsId());
                ps.setInt(8, compInstanceKpiDetailsBean.getMstProducerId());
                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            int generatedId = key != null ? key.intValue() : -1;

            log.debug("Successfully inserted non-group component instance KPI details with generated ID: {}", generatedId);
            return generatedId;

        } catch (Exception e) {
            log.error("Error occurred while adding non-group component instance KPI details for compInstanceId [{}], kpiId [{}]. Details: ",
                    compInstanceKpiDetailsBean.getCompInstanceId(), compInstanceKpiDetailsBean.getMstKpiDetailsId(), e);
            throw new HealControlCenterException("Error occurred while adding non-group component instance KPI details.");
        }
    }

    /**
     * Adds group component instance KPI details to the comp_instance_kpi_group_details table.
     *
     * @param compInstanceKpiGroupDetailsBean the CompInstanceKpiGroupDetailsBean containing the group KPI details
     * @return the generated component instance KPI group details ID
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public int addGroupCompInstanceKpiDetails(CompInstanceKpiGroupDetailsBean compInstanceKpiGroupDetailsBean) throws HealControlCenterException {
        String sql = "INSERT INTO comp_instance_kpi_group_details (attribute_value, created_time, updated_time, user_details_id, " +
                "comp_instance_id, mst_producer_kpi_mapping_id, collection_interval, mst_kpi_details_id, " +
                "is_discovery, kpi_group_name, mst_kpi_group_id, mst_producer_id, alias_name) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try {
            log.debug("Inserting group component instance KPI details for compInstanceId: {}, kpiGroupId: {}",
                    compInstanceKpiGroupDetailsBean.getCompInstanceId(), compInstanceKpiGroupDetailsBean.getMstKpiGroupId());

            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, compInstanceKpiGroupDetailsBean.getAttributeValue());
                ps.setString(2, compInstanceKpiGroupDetailsBean.getCreatedTime());
                ps.setString(3, compInstanceKpiGroupDetailsBean.getUpdatedTime());
                ps.setString(4, compInstanceKpiGroupDetailsBean.getUserDetailsId());
                ps.setInt(5, compInstanceKpiGroupDetailsBean.getCompInstanceId());
                ps.setInt(6, compInstanceKpiGroupDetailsBean.getMstProducerKpiMappingId());
                ps.setInt(7, compInstanceKpiGroupDetailsBean.getCollectionInterval());
                ps.setInt(8, compInstanceKpiGroupDetailsBean.getMstKpiDetailsId());
                ps.setInt(9, compInstanceKpiGroupDetailsBean.getIsDiscovery());
                ps.setString(10, compInstanceKpiGroupDetailsBean.getKpiGroupName());
                ps.setInt(11, compInstanceKpiGroupDetailsBean.getMstKpiGroupId());
                ps.setInt(12, compInstanceKpiGroupDetailsBean.getMstProducerId());
                ps.setString(13, compInstanceKpiGroupDetailsBean.getAliasName());
                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            int generatedId = key != null ? key.intValue() : -1;

            log.debug("Successfully inserted group component instance KPI details with generated ID: {}", generatedId);
            return generatedId;

        } catch (Exception e) {
            log.error("Error occurred while adding group component instance KPI details for compInstanceId [{}], kpiGroupId [{}]. Details: ",
                    compInstanceKpiGroupDetailsBean.getCompInstanceId(), compInstanceKpiGroupDetailsBean.getMstKpiGroupId(), e);
            throw new HealControlCenterException("Error occurred while adding group component instance KPI details.");
        }
    }

    /**
     * Adds component cluster mapping to the component_cluster_mapping table.
     *
     * @param compClusterMappingBean the CompClusterMappingBean containing the mapping details
     * @return the generated component cluster mapping ID
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public int addCompClusterMapping(CompClusterMappingBean compClusterMappingBean) throws HealControlCenterException {
        String sql = "INSERT INTO component_cluster_mapping (created_time, updated_time, user_details_id, account_id, comp_instance_id, cluster_id) " +
                "VALUES (?, ?, ?, ?, ?, ?)";

        try {
            log.debug("Inserting component cluster mapping for compInstanceId: {}, clusterId: {}",
                    compClusterMappingBean.getCompInstanceId(), compClusterMappingBean.getClusterId());

            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, compClusterMappingBean.getCreatedTime());
                ps.setString(2, compClusterMappingBean.getUpdatedTime());
                ps.setString(3, compClusterMappingBean.getUserDetailsId());
                ps.setInt(4, compClusterMappingBean.getAccountId());
                ps.setInt(5, compClusterMappingBean.getCompInstanceId());
                ps.setInt(6, compClusterMappingBean.getClusterId());
                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            int generatedId = key != null ? key.intValue() : -1;

            log.debug("Successfully inserted component cluster mapping with generated ID: {}", generatedId);
            return generatedId;

        } catch (Exception e) {
            log.error("Error occurred while adding component cluster mapping for compInstanceId [{}], clusterId [{}]. Details: ",
                    compClusterMappingBean.getCompInstanceId(), compClusterMappingBean.getClusterId(), e);
            throw new HealControlCenterException("Error occurred while adding component cluster mapping.");
        }
    }

    /**
     * Adds component instance attributes to the comp_instance_attribute_values table.
     *
     * @param attribute the CompInstanceAttributesBean containing the attribute details
     * @return the generated component instance attribute ID
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public int addComponentInstanceAttributes(CompInstanceAttributesBean attribute) throws HealControlCenterException {
        String sql = "INSERT INTO comp_instance_attribute_values (attribute_value, comp_instance_id, mst_component_attribute_mapping_id, " +
                "created_time, updated_time, user_details_id, mst_common_attributes_id, attribute_name) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

        try {
            log.debug("Inserting component instance attributes for compInstanceId: {}, attributeName: {}",
                    attribute.getCompInstanceId(), attribute.getAttributeName());

            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, attribute.getAttributeValue());
                ps.setInt(2, attribute.getCompInstanceId());
                ps.setInt(3, attribute.getMstComponentAttributeMappingId());
                ps.setString(4, attribute.getCreatedTime());
                ps.setString(5, attribute.getUpdatedTime());
                ps.setString(6, attribute.getUserDetailsId());
                ps.setInt(7, attribute.getMstCommonAttributesId());
                ps.setString(8, attribute.getAttributeName());
                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            int generatedId = key != null ? key.intValue() : -1;

            log.debug("Successfully inserted component instance attributes with generated ID: {}", generatedId);
            return generatedId;

        } catch (Exception e) {
            log.error("Error occurred while adding component instance attributes for compInstanceId [{}], attributeName [{}]. Details: ",
                    attribute.getCompInstanceId(), attribute.getAttributeName(), e);
            throw new HealControlCenterException("Error occurred while adding component instance attributes.");
        }
    }

    /**
     * Updates component instance attributes in the comp_instance_attribute_values table.
     *
     * @param attribute the CompInstanceAttributesBean containing the updated attribute details
     * @return the number of rows affected by the update operation
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public int updateComponentInstanceAttributes(CompInstanceAttributesBean attribute) throws HealControlCenterException {
        String sql = "UPDATE comp_instance_attribute_values SET attribute_value = ?, updated_time = ?, user_details_id = ? " +
                "WHERE comp_instance_id = ? AND mst_component_attribute_mapping_id = ? AND mst_common_attributes_id = ?";

        try {
            log.debug("Updating component instance attributes for compInstanceId: {}, attributeName: {}",
                    attribute.getCompInstanceId(), attribute.getAttributeName());

            int rowsAffected = jdbcTemplate.update(sql,
                    attribute.getAttributeValue(),
                    attribute.getUpdatedTime(),
                    attribute.getUserDetailsId(),
                    attribute.getCompInstanceId(),
                    attribute.getMstComponentAttributeMappingId(),
                    attribute.getMstCommonAttributesId()
            );

            log.debug("Successfully updated component instance attributes. Rows affected: {}", rowsAffected);
            return rowsAffected;

        } catch (Exception e) {
            log.error("Error occurred while updating component instance attributes for compInstanceId [{}], attributeName [{}]. Details: ",
                    attribute.getCompInstanceId(), attribute.getAttributeName(), e);
            throw new HealControlCenterException("Error occurred while updating component instance attributes.");
        }
    }

    /**
     * Adds agent component instance mappings in batch to the agent_comp_instance_mapping table.
     *
     * @param beanList the list of AgentCompInstMappingBean containing the mapping details
     * @return array of affected row counts for each batch operation
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public int[] addAgentCompInstMapping(List<AgentCompInstMappingBean> beanList) throws HealControlCenterException {
        if (beanList == null || beanList.isEmpty()) {
            log.warn("Empty or null bean list provided for agent component instance mapping");
            return new int[0];
        }

        String sql = "INSERT INTO agent_comp_instance_mapping (comp_instance_id, agent_id, created_time) VALUES (?, ?, ?)";

        try {
            log.debug("Inserting {} agent component instance mappings in batch", beanList.size());

            int[] result = jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    AgentCompInstMappingBean bean = beanList.get(i);
                    ps.setInt(1, bean.getCompInstanceId());
                    ps.setInt(2, bean.getAgentId());
                    ps.setString(3, bean.getCreatedTime());
                }

                @Override
                public int getBatchSize() {
                    return beanList.size();
                }
            });

            log.debug("Successfully inserted {} agent component instance mappings in batch", result.length);
            return result;

        } catch (Exception e) {
            log.error("Error occurred while adding agent component instance mappings in batch. Bean list size: [{}]. Details: ",
                    beanList.size(), e);
            throw new HealControlCenterException("Error occurred while adding agent component instance mappings in batch.");
        }
    }

    /**
     * Retrieves active component instance by identifier and name from comp_instance table.
     *
     * @param parentIdentifier the component instance identifier
     * @param parentName the component instance name
     * @param accountId the account ID
     * @return ComponentInstanceBean containing component instance details, or null if not found
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public ComponentInstanceBean getActiveComponentInstanceByIdentifierAndName(String parentIdentifier, String parentName, int accountId) throws HealControlCenterException {
        String query = "SELECT id, name, status, host_id AS hostId, is_dr AS isDR, is_cluster AS isCluster, " +
                "mst_component_version_id AS mstComponentVersionId, created_time AS createdTime, updated_time AS updatedTime, " +
                "user_details_id AS userDetailsId, account_id AS accountId, mst_component_id AS mstComponentId, " +
                "mst_component_type_id AS mstComponentTypeId, discovery, host_address AS hostAddress, identifier, " +
                "mst_common_version_id AS mstCommonVersionId, supervisor_id AS supervisorId " +
                "FROM comp_instance WHERE (identifier = ? OR name = ?) AND account_id = ? AND status = 1";

        try {
            log.debug("Querying active comp_instance for identifier: {}, name: {}, accountId: {}", parentIdentifier, parentName, accountId);

            return jdbcTemplate.queryForObject(query, (rs, rowNum) -> {
                ComponentInstanceBean componentInstance = new ComponentInstanceBean();
                componentInstance.setId(rs.getInt("id"));
                componentInstance.setName(rs.getString("name"));
                componentInstance.setStatus(rs.getInt("status"));
                componentInstance.setHostId(rs.getInt("hostId"));
                componentInstance.setIsDR(rs.getInt("isDR"));
                componentInstance.setIsCluster(rs.getInt("isCluster"));
                componentInstance.setMstComponentVersionId(rs.getInt("mstComponentVersionId"));
                componentInstance.setCreatedTime(rs.getString("createdTime"));
                componentInstance.setUpdatedTime(rs.getString("updatedTime"));
                componentInstance.setUserDetailsId(rs.getString("userDetailsId"));
                componentInstance.setAccountId(rs.getInt("accountId"));
                componentInstance.setMstComponentId(rs.getInt("mstComponentId"));
                componentInstance.setMstComponentTypeId(rs.getInt("mstComponentTypeId"));
                componentInstance.setDiscovery(rs.getInt("discovery"));
                componentInstance.setHostAddress(rs.getString("hostAddress"));
                componentInstance.setIdentifier(rs.getString("identifier"));
                componentInstance.setMstCommonVersionId(rs.getInt("mstCommonVersionId"));
                componentInstance.setSupervisorId(rs.getInt("supervisorId"));

                return componentInstance;
            }, parentIdentifier, parentName, accountId);

        } catch (Exception e) {
            log.error("Error occurred while fetching active component instance by identifier [{}] or name [{}] and accountId [{}]. Details: ",
                    parentIdentifier, parentName, accountId, e);
            throw new HealControlCenterException("Error occurred while fetching active component instance by identifier and name.");
        }
    }

    /**
     * Retrieves component cluster mapping details by joining component_cluster_mapping and comp_instance tables.
     * This method uses JOIN as it requires data from multiple tables.
     *
     * @param parentIdentifier the component instance identifier
     * @param parentName the component instance name
     * @param accountId the account ID
     * @return CompClusterMappingBean containing cluster mapping details, or null if not found
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public CompClusterMappingBean getCompClusterDetails(String parentIdentifier, String parentName, int accountId) throws HealControlCenterException {
        String query = "SELECT cm.id, cm.created_time AS createdTime, cm.updated_time AS updatedTime, " +
                "cm.user_details_id AS userDetailsId, cm.account_id AS accountId, " +
                "ci.id AS compInstanceId, cm.cluster_id AS clusterId " +
                "FROM component_cluster_mapping cm " +
                "JOIN comp_instance ci ON ci.id = cm.comp_instance_id " +
                "WHERE (ci.identifier = ? OR ci.name = ?) AND cm.account_id = ?";

        try {
            log.debug("Querying component cluster mapping details for identifier: {}, name: {}, accountId: {}",
                    parentIdentifier, parentName, accountId);

            return jdbcTemplate.queryForObject(query, (rs, rowNum) -> {
                CompClusterMappingBean clusterMapping = new CompClusterMappingBean();
                clusterMapping.setId(rs.getInt("id"));
                clusterMapping.setCreatedTime(rs.getString("createdTime"));
                clusterMapping.setUpdatedTime(rs.getString("updatedTime"));
                clusterMapping.setUserDetailsId(rs.getString("userDetailsId"));
                clusterMapping.setAccountId(rs.getInt("accountId"));
                clusterMapping.setCompInstanceId(rs.getInt("compInstanceId"));
                clusterMapping.setClusterId(rs.getInt("clusterId"));

                return clusterMapping;
            }, parentIdentifier, parentName, accountId);

        } catch (Exception e) {
            log.error("Error occurred while fetching component cluster mapping details for identifier [{}] or name [{}] and accountId [{}]. Details: ",
                    parentIdentifier, parentName, accountId, e);
            throw new HealControlCenterException("Error occurred while fetching component cluster mapping details.");
        }
    }

    /**
     * Custom row mapper for GetCompInstance.
     */
    private static class GetCompInstanceRowMapper implements RowMapper<GetCompInstance> {
        @Override
        public GetCompInstance mapRow(ResultSet rs, int rowNum) throws SQLException {
            return GetCompInstance.builder()
                    .instanceId(String.valueOf(rs.getInt("id")))
                    .instanceName(rs.getString("name"))
                    .instanceIdentifier(rs.getString("identifier"))
                    .hostId(String.valueOf(rs.getInt("host_id")))
                    .hostName(rs.getString("host_name"))
                    .hostIdentifier(rs.getString("host_identifier"))
                    .hostAddress(Collections.singletonList(rs.getString("host_address")))
                    .componentId(rs.getInt("mst_component_id"))
                    .componentName(rs.getString("component_name"))
                    .componentTypeId(rs.getInt("mst_component_type_id"))
                    .componentTypeName(rs.getString("component_type_name"))
                    .componentVersionId(rs.getInt("mst_component_version_id"))
                    .componentVersionName(rs.getString("component_version_name"))
                    .commonVersionId(rs.getInt("common_version_id"))
                    .commonVersionName(rs.getString("common_version_name"))
                    .status(DiscoveryStatus.ADDED_TO_SYSTEM)
                    .process(rs.getInt("discovery") == 0 ? "Manual" : "Auto")
                    .environment(String.valueOf(rs.getInt("is_DR")))
                    .lastDiscoveryRunTime(rs.getDate("updated_time").getTime())
                    .build();
        }
    }

    /**
     * Retrieves service-application mappings for the specified account.
     * @param accountId The account ID for which to fetch mappings
     * @return Map of service IDs to list of applications
     * @throws HealControlCenterException if database operations fail
     */
    public Map<Integer, List<IdNamePojo>> getServiceApplicationMappings(int accountId) throws HealControlCenterException {
        String query = "SELECT service_id, service_name, application_id, application_name " +
                "FROM view_application_service_mapping WHERE account_id = ?";

        try {
            List<Map<String, Object>> results = jdbcTemplate.queryForList(query, accountId);
            Map<Integer, List<IdNamePojo>> serviceAppMap = new HashMap<>();

            for (Map<String, Object> row : results) {
                Integer serviceId = (Integer) row.get("service_id");
                IdNamePojo application = IdNamePojo.builder()
                        .id((Integer) row.get("application_id"))
                        .name((String) row.get("application_name"))
                        .build();

                serviceAppMap.computeIfAbsent(serviceId, k -> new ArrayList<>()).add(application);
            }

            return serviceAppMap;
        } catch (Exception e) {
            log.error("Error occurred while fetching service-application mappings for accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while fetching service-application mappings.");
        }
    }

    /**
     * Retrieves agent mappings for the specified account.
     * @param accountId The account ID for which to fetch mappings
     * @return Map of instance IDs to list of agents
     * @throws HealControlCenterException if database operations fail
     */
    public Map<Integer, List<AgentDetails>> getInstanceAgentMappings(int accountId) throws HealControlCenterException {
        String query = "SELECT acim.comp_instance_id, a.id as agent_id, a.name as agent_name " +
                "FROM agent_comp_instance_mapping acim " +
                "JOIN agent a ON acim.agent_id = a.id " +
                "JOIN comp_instance ci ON acim.comp_instance_id = ci.id " +
                "WHERE ci.account_id = ? AND ci.status = 1";

        try {
            List<Map<String, Object>> results = jdbcTemplate.queryForList(query, accountId);
            Map<Integer, List<AgentDetails>> agentMap = new HashMap<>();

            for (Map<String, Object> row : results) {
                Integer instanceId = (Integer) row.get("comp_instance_id");
                AgentDetails agent = AgentDetails.builder()
                        .agentId((Integer) row.get("agent_id"))
                        .agentName((String) row.get("agent_name"))
                        .build();

                agentMap.computeIfAbsent(instanceId, k -> new ArrayList<>()).add(agent);
            }

            return agentMap;
        } catch (Exception e) {
            log.error("Error occurred while fetching instance-agent mappings for accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while fetching instance-agent mappings.");
        }
    }

    /**
     * Retrieves services for the specified instance.
     * @param accountId The account ID for which to fetch services
     * @param instanceIdentifiers The instance identifier for which to fetch services
     * @return Map of instance identifiers to list of services
     * @throws HealControlCenterException if database operations fail
     */
    public Map<String, List<IdNamePojo>> getServicesForInstances(int accountId, List<String> instanceIdentifiers) throws HealControlCenterException {
        String query = "SELECT DISTINCT ci.identifier, vcs.service_id, vcs.service_name " +
                "FROM view_cluster_services vcs " +
                "JOIN component_cluster_mapping ccm ON vcs.id = ccm.cluster_id " +
                "JOIN comp_instance ci ON ccm.comp_instance_id = ci.id " +
                "WHERE ci.account_id = ? AND ci.identifier IN (<instanceIdentifiers>) AND ci.status = 1";

        try {
            String inSql = String.join(",", Collections.nCopies(instanceIdentifiers.size(), "?"));
            List<Object> params = new ArrayList<>();
            params.add(accountId);
            params.addAll(instanceIdentifiers);

            List<Map<String, Object>> results = jdbcTemplate.queryForList(query.replace("<instanceIdentifiers>", inSql), params.toArray());
            Map<String, List<IdNamePojo>> instanceServiceMap = new HashMap<>();

            for (Map<String, Object> row : results) {
                String instanceIdentifier = (String) row.get("identifier");
                IdNamePojo service = IdNamePojo.builder()
                        .id((Integer) row.get("service_id"))
                        .name((String) row.get("service_name"))
                        .build();

                instanceServiceMap.computeIfAbsent(instanceIdentifier, k -> new ArrayList<>()).add(service);
            }

            return instanceServiceMap;
        } catch (Exception e) {
            log.error("Error occurred while fetching services for instances and accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while fetching services for instances.");
        }
    }

    /**
     * Retrieves port attributes for the specified instances.
     * @param accountId The account ID for which to fetch port attributes
     * @param instanceIdentifiers The instance identifiers for which to fetch port attributes
     * @return Map of instance identifiers to list of port attributes
     * @throws HealControlCenterException if database operations fail
     */
    public Map<String, List<GetCompInstance.AttributeNameValue>> getPortAttributesForInstances(int accountId, List<String> instanceIdentifiers) throws HealControlCenterException {
        String query = "SELECT ci.identifier, mca.attribute_name, ciav.attribute_value " +
                "FROM comp_instance_attribute_values ciav " +
                "JOIN mst_common_attributes mca ON ciav.mst_common_attributes_id = mca.id " +
                "JOIN comp_instance ci ON ciav.comp_instance_id = ci.id " +
                "WHERE ci.account_id = ? AND ci.identifier IN (<instanceIdentifiers>) AND mca.attribute_name = 'MonitorPort'";

        try {
            String inSql = String.join(",", Collections.nCopies(instanceIdentifiers.size(), "?"));
            List<Object> params = new ArrayList<>();
            params.add(accountId);
            params.addAll(instanceIdentifiers);

            List<Map<String, Object>> results = jdbcTemplate.queryForList(query.replace("<instanceIdentifiers>", inSql), params.toArray());
            Map<String, List<GetCompInstance.AttributeNameValue>> instancePortMap = new HashMap<>();

            for (Map<String, Object> row : results) {
                String instanceIdentifier = (String) row.get("identifier");
                GetCompInstance.AttributeNameValue port = GetCompInstance.AttributeNameValue.builder()
                        .attributeName((String) row.get("attribute_name"))
                        .attributeValue((String) row.get("attribute_value"))
                        .build();

                instancePortMap.computeIfAbsent(instanceIdentifier, k -> new ArrayList<>()).add(port);
            }

            return instancePortMap;
        } catch (Exception e) {
            log.error("Error occurred while fetching port attributes for instances and accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while fetching port attributes for instances.");
        }
    }

    /**
     * Retrieves paginated auto-discovery component instances for the specified account with optional search filtering.
     * @param accountId The account ID for which to fetch instances
     * @param searchTerm Optional search term to filter instances
     * @param pageable Pagination parameters
     * @return Paginated list of auto-discovery component instances for the specified account
     * @throws HealControlCenterException if database operations fail
     */
    public List<GetCompInstance> getAutoDiscoveryInstances(int accountId, String searchTerm, int hostComponentTypeId, Pageable pageable) throws HealControlCenterException {
        String baseQuery = "SELECT ap.process_name as instanceName, ap.process_identifier as instanceIdentifier, ah.hostname as hostName," +
                "ap.host_identifier as hostIdentifier, ada.attribute_name as attributeName,ada.attribute_value as attributeValue," +
                "ah.environment as isDR,ap.discovery_status as status,ah.discovery_status as hostStatus," +
                "ah.last_discovery_run_time as lastDiscoveryRunTime,ap.component_id as componentId,vc.component_name as componentName," +
                "ap.component_version_id as componentVersionId,vc.component_version_name as componentVersionName," +
                "vc.common_version_id as commonVersionId,vc.common_version_name as commonVersionName,ap.component_type_id as componentTypeId," +
                "vc.component_type_name as componentTypeName,vasm.service_id as serviceId,vasm.service_name as serviceName," +
                "vasm.service_identifier as serviceIdentifier,vasm.application_id as applicationId,vasm.application_name as applicationName," +
                "vasm.application_identifier as applicationIdentifier,ap.account_id as accountId,ah.is_ignored as isIgnored," +
                "ap.is_blacklisted as isBlacklisted " +
                "FROM autodisco_process ap JOIN autodisco_host ah ON ap.host_identifier = ah.host_identifier " +
                "LEFT JOIN autodisco_discovered_attributes ada ON ap.process_identifier = ada.discovered_attributes_identifier " +
                "AND LOWER(ada.entity_type) = 'compinstance' " +
                "LEFT JOIN view_components vc ON vc.component_id = ap.component_id AND vc.component_version_id = ap.component_version_id " +
                "AND vc.component_type_id = ap.component_type_id " +
                "LEFT JOIN autodisco_service_mapping asm ON ap.process_identifier = asm.service_mapping_identifier AND LOWER(asm.entity_type) = 'compinstance' " +
                "LEFT JOIN view_application_service_mapping vasm ON asm.service_identifier = vasm.service_identifier WHERE ah.is_ignored = 0 " +
                "AND ap.is_blacklisted = '0' AND ap.component_id != 0 AND ap.is_ignored = 0 AND ap.account_id = ? " +
                "AND ap.component_type_id != ?";

        List<Object> params = new ArrayList<>();
        params.add(accountId);
        params.add(hostComponentTypeId);

        // Add search filter if provided
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(ap.process_name) LIKE ? OR LOWER(vc.component_name) LIKE ? OR LOWER(ah.hostname) LIKE ?)";
            String searchPattern = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(searchPattern);
            params.add(searchPattern);
            params.add(searchPattern);
        }

        // Apply pagination
        String paginatedQuery = PaginationUtils.applyPagination(baseQuery, pageable);
        params = PaginationUtils.buildPaginationParams(params, pageable);

        try {
            log.debug("Fetching paginated auto-discovery instances for accountId: {} with searchTerm: {}", accountId, searchTerm);
            return jdbcTemplate.query(paginatedQuery, new AutoDiscoveryInstanceRowMapper(), params.toArray());
        } catch (Exception e) {
            log.error("Error occurred while fetching paginated auto-discovery instances for accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while fetching paginated auto-discovery instances.");
        }
    }

    /**
     * Counts total auto-discovery component instances for the specified account with optional search filtering.
     * @param accountId The account ID for which to count instances
     * @param searchTerm Optional search term to filter instances
     * @return Total count of auto-discovery component instances
     * @throws HealControlCenterException if database operations fail
     */
    public int countAutoDiscoveryInstances(int accountId, String searchTerm, int hostComponentTypeId) throws HealControlCenterException {
        String baseQuery = "SELECT COUNT(DISTINCT ap.process_identifier) FROM autodisco_process ap " +
                "JOIN autodisco_host ah ON ap.host_identifier = ah.host_identifier " +
                "LEFT JOIN view_components vc ON vc.component_id = ap.component_id AND vc.component_version_id = ap.component_version_id " +
                "AND vc.component_type_id = ap.component_type_id " +
                "WHERE ah.is_ignored = 0 AND ap.is_blacklisted = '0' AND ap.component_id != 0 AND ap.is_ignored = 0 AND ap.account_id = ? AND ap.component_type_id != ?";

        List<Object> params = new ArrayList<>();
        params.add(accountId);
        params.add(hostComponentTypeId);

        // Add search filter if provided
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            baseQuery += " AND (LOWER(ap.process_name) LIKE ? OR LOWER(vc.component_name) LIKE ? OR LOWER(ah.hostname) LIKE ?)";
            String searchPattern = "%" + searchTerm.toLowerCase().trim() + "%";
            params.add(searchPattern);
            params.add(searchPattern);
            params.add(searchPattern);
        }

        try {
            log.debug("Counting auto-discovery instances for accountId: {} with searchTerm: {}", accountId, searchTerm);
            return jdbcTemplate.queryForObject(baseQuery, Integer.class, params.toArray());
        } catch (Exception e) {
            log.error("Error occurred while counting auto-discovery instances for accountId: {}. Details: {}", accountId, e.getMessage());
            throw new HealControlCenterException("Error occurred while counting auto-discovery instances.");
        }
    }

    /**
     * Row mapper for auto-discovery instances
     */
    private static class AutoDiscoveryInstanceRowMapper implements RowMapper<GetCompInstance> {
        @Override
        public GetCompInstance mapRow(ResultSet rs, int rowNum) throws SQLException {

           return GetCompInstance.builder()
                            .instanceId(rs.getString("instanceIdentifier"))
                            .instanceName(rs.getString("instanceName"))
                            .instanceIdentifier(rs.getString("instanceIdentifier"))
                            .hostId(rs.getString("hostIdentifier"))
                            .hostName(rs.getString("hostName"))
                            .hostIdentifier(rs.getString("hostIdentifier"))
                            .componentId(rs.getInt("componentId"))
                            .componentName(rs.getString("componentName"))
                            .componentVersionId(rs.getInt("componentVersionId"))
                            .componentVersionName(rs.getString("componentVersionName"))
                            .commonVersionId(rs.getInt("commonVersionId"))
                            .commonVersionName(rs.getString("commonVersionName"))
                            .componentTypeId(rs.getInt("componentTypeId"))
                            .componentTypeName(rs.getString("componentTypeName"))
                            .service(Collections.singletonList(IdNamePojo.builder()
                                    .id(rs.getInt("serviceId"))
                                    .name(rs.getString("serviceName"))
                                    .identifier(rs.getString("serviceIdentifier"))
                                    .build()))
                            .application(Collections.singletonList(IdNamePojo.builder()
                                    .id(rs.getInt("applicationId"))
                                    .name(rs.getString("applicationName"))
                                    .identifier(rs.getString("applicationIdentifier"))
                                    .build()))
                            .port(new LinkedList<>(Collections.singletonList(GetCompInstance.AttributeNameValue.builder()
                                    .attributeName(rs.getString("attributeName"))
                                    .attributeValue(rs.getString("attributeValue"))
                                    .build())))
                            .status(DiscoveryStatus.valueOf(rs.getString("status")))
                            .process("Auto")
                            .environment(rs.getInt("isDR") == 1 ? "DR" : "Production")
                            .lastDiscoveryRunTime(rs.getTimestamp("lastDiscoveryRunTime") != null ? rs.getTimestamp("lastDiscoveryRunTime").getTime() : 0L)
                            .hostStatus(DiscoveryStatus.valueOf(rs.getString("hostStatus")))
                            .build();
        }
    }

    public MasterComponentTypeBean findByNameAndAccountId(String name, int accountId) {
        String sql = "SELECT id, name, description, is_custom as isCustom, status, " +
                "created_time as createdTime, updated_time as updatedTime, " +
                "user_details_id as userDetailsId, account_id as accountId " +
                "FROM mst_component_type " +
                "WHERE account_id IN (1, ?) AND LOWER(name) = LOWER(?)";

        List<MasterComponentTypeBean> results = jdbcTemplate.query(sql,
                new BeanPropertyRowMapper<>(MasterComponentTypeBean.class), accountId, name);

        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * Inserts a new component instance into the database.
     * @param componentInstance The component instance bean to insert
     * @return The generated ID of the inserted component instance
     * @throws HealControlCenterException if insertion fails
     */
    public int insertComponentInstance(ComponentInstanceBean componentInstance) throws HealControlCenterException {
        String sql = "INSERT INTO comp_instance (name, host_id, is_DR, is_cluster, mst_component_version_id, " +
                "created_time, updated_time, user_details_id, account_id, mst_component_id, mst_component_type_id, " +
                "discovery, host_address, identifier, mst_common_version_id, parent_instance_id, supervisor_id, status) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        KeyHolder keyHolder = new GeneratedKeyHolder();

        try {
            int rowsAffected = jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, componentInstance.getName());
                ps.setInt(2, componentInstance.getHostId());
                ps.setInt(3, componentInstance.getIsDR());
                ps.setInt(4, componentInstance.getIsCluster());
                ps.setInt(5, componentInstance.getMstComponentVersionId());
                ps.setString(6, componentInstance.getCreatedTime());
                ps.setString(7, componentInstance.getUpdatedTime());
                ps.setString(8, componentInstance.getUserDetailsId());
                ps.setInt(9, componentInstance.getAccountId());
                ps.setInt(10, componentInstance.getMstComponentId());
                ps.setInt(11, componentInstance.getMstComponentTypeId());
                ps.setInt(12, componentInstance.getDiscovery());
                ps.setString(13, componentInstance.getHostAddress());
                ps.setString(14, componentInstance.getIdentifier());
                ps.setInt(15, componentInstance.getMstCommonVersionId());
                ps.setInt(16, componentInstance.getParentId());
                ps.setInt(17, componentInstance.getSupervisorId());
                ps.setInt(18, componentInstance.getStatus());
                return ps;
            }, keyHolder);

            if (rowsAffected > 0 && keyHolder.getKey() != null) {
                int generatedId = keyHolder.getKey().intValue();
                log.debug("Successfully inserted component instance with ID: {}", generatedId);
                return generatedId;
            } else {
                throw new HealControlCenterException("Failed to insert component instance - no rows affected");
            }
        } catch (Exception e) {
            log.error("Error occurred while inserting component instance: {}", e.getMessage(), e);
            throw new HealControlCenterException("Error occurred while inserting component instance: " + e.getMessage());
        }
    }

    /**
     * Inserts instance metadata for a component instance.
     * @param instanceId The component instance ID
     * @param userDetailsId The user who created the instance
     * @param createdTime The creation timestamp
     * @param updatedTime The update timestamp
     * @return The generated metadata ID
     * @throws HealControlCenterException if insertion fails
     */
    public int insertInstanceMetadata(int instanceId, String userDetailsId, String createdTime, String updatedTime) throws HealControlCenterException {
        String sql = "INSERT INTO instance_metadata (instance_id, environment_id, created_time, updated_time, user_details_id, status) " +
                "VALUES (?, ?, ?, ?, ?, ?)";

        KeyHolder keyHolder = new GeneratedKeyHolder();

        try {
            int rowsAffected = jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setInt(1, instanceId);
                ps.setInt(2, 383); // Default environment ID as per original code
                ps.setString(3, createdTime);
                ps.setString(4, updatedTime);
                ps.setString(5, userDetailsId);
                ps.setInt(6, 1); // Active status
                return ps;
            }, keyHolder);

            if (rowsAffected > 0 && keyHolder.getKey() != null) {
                int generatedId = keyHolder.getKey().intValue();
                log.debug("Successfully inserted instance metadata with ID: {} for instance: {}", generatedId, instanceId);
                return generatedId;
            } else {
                throw new HealControlCenterException("Failed to insert instance metadata - no rows affected");
            }
        } catch (Exception e) {
            log.error("Error occurred while inserting instance metadata for instance {}: {}", instanceId, e.getMessage(), e);
            throw new HealControlCenterException("Error occurred while inserting instance metadata: " + e.getMessage());
        }
    }

    /**
     * Checks if a component instance with the given identifier already exists for the account.
     * @param identifier The component instance identifier
     * @param accountId The account ID
     * @return true if exists, false otherwise
     */
    public boolean componentInstanceExists(String identifier, int accountId) {
        String sql = "SELECT COUNT(*) FROM comp_instance WHERE identifier = ? AND account_id = ?";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, identifier, accountId);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("Error occurred while checking component instance existence: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Gets instance details by host address.
     * @param accountId The account ID
     * @param hostAddress The host address
     * @param isDR The environment/DR value
     * @return Count of instances or -1 on error
     */
    public int getInstanceDetailsByHostAddress(int accountId, String hostAddress, int isDR) {
        String sql = "SELECT COUNT(*) FROM comp_instance WHERE account_id = ? AND host_address = ? AND is_DR = ?";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, accountId, hostAddress, isDR);
            return count != null ? count : -1;
        } catch (Exception e) {
            log.error("Error getting instance details by host address: {}", e.getMessage(), e);
            return -1;
        }
    }

    /**
     * Checks host address monitor port existence.
     * @param accountId The account ID
     * @param hostAddress The host address
     * @param monitorPort The monitor port
     * @return Count of instances or -1 on error
     */
    public int checkHostAddressMonitorPortExistance(int accountId, String hostAddress, String monitorPort) {
        String sql = "SELECT COUNT(*) FROM comp_instance ci, comp_instance_attribute_values ciav, mst_common_attributes mca " +
                "WHERE ci.id = ciav.comp_instance_id AND ciav.mst_common_attributes_id = mca.id " +
                "AND ci.account_id = ? AND ci.host_address = ? AND mca.attribute_name = 'MonitorPort' AND ciav.attribute_value = ?";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, accountId, hostAddress, monitorPort);
            return count != null ? count : -1;
        } catch (Exception e) {
            log.error("Error checking host address monitor port existence: {}", e.getMessage(), e);
            return -1;
        }
    }

    /**
     * Checks host address existence.
     * @param accountId The account ID
     * @param hostAddress The host address
     * @return Count of instances
     */
    public int checkHostAddressExistance(int accountId, String hostAddress) {
        String sql = "SELECT COUNT(*) FROM comp_instance WHERE account_id = ? AND host_address = ? AND mst_component_type_id = 1";

        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, accountId, hostAddress);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("Error checking host address existence: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * Gets instance cluster service details with complete cluster and instance information.
     * @param serviceId The service ID
     * @param accountId The account ID
     * @return List of cluster instance service details
     */
    public List<InstanceClusterServicePojo> getInstanceClusterServiceDetails(int serviceId, int accountId) {
        String sql = "SELECT vc.id AS clusterId, vc.name AS clusterName, vc.identifier AS clusterIdentifier, " +
                "vc.service_id AS serviceId, vc.service_name AS serviceName, vc.service_identifier AS serviceIdentifier, " +
                "vci.mst_component_id AS clusterComponentId, vci.component_name AS clusterComponentName, " +
                "vci.mst_component_type_id AS clusterComponentTypeId, vci.component_type_name AS clusterComponentTypeName, " +
                "vci.mst_component_version_id AS clusterComponentVersionId, vci.component_version_name AS clusterComponentVersionName, " +
                "vci.common_version_id AS clusterCommonVersionId, vci.common_version_name AS clusterCommonVersionName, " +
                "ccm.comp_instance_id AS instanceId, vcii.name AS instanceName, vcii.identifier AS instanceIdentifier, " +
                "vcii.host_address AS hostAddress, vcii.mst_component_id AS instanceComponentId, " +
                "vcii.component_name AS instanceComponentName, vcii.mst_component_type_id AS instanceComponentTypeId, " +
                "vcii.component_type_name AS instanceComponentTypeName, vcii.mst_component_version_id AS instanceComponentVersionId, " +
                "vcii.component_version_name AS instanceComponentVersionName, vcii.common_version_id AS instanceCommonVersionId, " +
                "vcii.common_version_name AS instanceCommonVersionName " +
                "FROM view_cluster_services vc " +
                "JOIN view_component_instance vci ON vc.id = vci.id " +
                "LEFT JOIN component_cluster_mapping ccm ON ccm.cluster_id = vc.id " +
                "LEFT JOIN view_component_instance vcii ON ccm.comp_instance_id = vcii.id " +
                "WHERE vci.account_id = ? AND vc.service_id = ?";

        try {
            log.debug("Fetching cluster instance service details for serviceId: {} and accountId: {}", serviceId, accountId);
            return jdbcTemplate.query(sql, (rs, rowNum) -> {
                 return InstanceClusterServicePojo.builder()
                        .clusterId(rs.getInt("clusterId"))
                        .clusterName(rs.getString("clusterName"))
                        .clusterIdentifier(rs.getString("clusterIdentifier"))
                        .serviceId(rs.getInt("serviceId"))
                        .serviceName(rs.getString("serviceName"))
                        .serviceIdentifier(rs.getString("serviceIdentifier"))
                        .clusterComponentId(rs.getInt("clusterComponentId"))
                        .clusterComponentName(rs.getString("clusterComponentName"))
                        .clusterComponentTypeId(rs.getInt("clusterComponentTypeId"))
                        .clusterComponentTypeName(rs.getString("clusterComponentTypeName"))
                        .clusterComponentVersionId(rs.getInt("clusterComponentVersionId"))
                        .clusterComponentVersionName(rs.getString("clusterComponentVersionName"))
                        .clusterCommonVersionId(rs.getInt("clusterCommonVersionId"))
                        .clusterCommonVersionName(rs.getString("clusterCommonVersionName"))
                        .instanceId(rs.getInt("instanceId"))
                        .instanceName(rs.getString("instanceName"))
                        .instanceIdentifier(rs.getString("instanceIdentifier"))
                        .hostAddress(rs.getString("hostAddress"))
                        .instanceComponentId(rs.getInt("instanceComponentId"))
                        .instanceComponentName(rs.getString("instanceComponentName"))
                        .instanceComponentTypeId(rs.getInt("instanceComponentTypeId"))
                        .instanceComponentTypeName(rs.getString("instanceComponentTypeName"))
                        .instanceComponentVersionId(rs.getInt("instanceComponentVersionId"))
                        .instanceComponentVersionName(rs.getString("instanceComponentVersionName"))
                        .instanceCommonVersionId(rs.getInt("instanceCommonVersionId"))
                        .instanceCommonVersionName(rs.getString("instanceCommonVersionName"))
                        .build();
            }, accountId, serviceId);
        } catch (Exception e) {
            log.error("Error getting cluster instance service details for serviceId: {} and accountId: {}. Details: {}",
                    serviceId, accountId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Saves component instance attribute.
     * @param attribute Component instance attribute bean
     * @return Generated attribute ID
     */
    @Transactional
    public int saveComponentInstanceAttribute(CompInstanceAttributesBean attribute) {
        String sql = "INSERT INTO comp_instance_attribute_values (comp_instance_id, mst_common_attributes_id, " +
                "attribute_value, user_details_id, created_time, updated_time) " +
                "VALUES (?, ?, ?, ?, NOW(), NOW())";

        try {
            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setInt(1, attribute.getCompInstanceId());
                ps.setInt(2, attribute.getMstCommonAttributesId());
                ps.setString(3, attribute.getAttributeValue());
                ps.setString(4, attribute.getUserDetailsId());
                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            return key != null ? key.intValue() : -1;

        } catch (Exception e) {
            log.error("Error saving component instance attribute: {}", e.getMessage(), e);
            return -1;
        }
    }

    /**
     * Saves agent instance mapping.
     * @param instanceId Component instance ID
     * @param agentId Agent ID
     * @return Generated mapping ID
     */
    @Transactional
    public int saveAgentInstanceMapping(int instanceId, int agentId) {
        String sql = "INSERT INTO agent_comp_instance_mapping (agent_id, comp_instance_id, created_time, updated_time) " +
                "VALUES (?, ?, NOW(), NOW())";

        try {
            KeyHolder keyHolder = new GeneratedKeyHolder();

            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setInt(1, agentId);
                ps.setInt(2, instanceId);
                return ps;
            }, keyHolder);

            Number key = keyHolder.getKey();
            return key != null ? key.intValue() : -1;

        } catch (Exception e) {
            log.error("Error saving agent instance mapping: {}", e.getMessage(), e);
            return -1;
        }
    }

    /**
     * Gets instance attribute details for component instance ID
     * @param accountId Account ID
     * @param compInstanceId Component instance ID
     * @return List of InstanceAttributes
     */
    public List<com.heal.configuration.pojos.InstanceAttributes> getInstanceAttributeDetailsForCompInstanceId(int accountId, int compInstanceId) {
        String sql = "SELECT cia.id attributeId, mca.attribute_name attributeName, cia.attribute_value attributeValue, " +
                "cia.comp_instance_id compInstanceId, mcam.is_ui_visible isUiVisible, mca.attribute_type attributeType, " +
                "mca.status, mca.is_custom isCustom " +
                "FROM comp_instance_attribute_values cia " +
                "JOIN mst_component_attribute_mapping mcam ON cia.mst_component_attribute_mapping_id = mcam.id " +
                "JOIN mst_common_attributes mca ON cia.mst_common_attributes_id = mca.id " +
                "WHERE (mca.account_id = 1 OR mca.account_id = ?) " +
                "AND cia.comp_instance_id = ?";

        try {
            return jdbcTemplate.query(sql, (rs, rowNum) -> {
                com.heal.configuration.pojos.InstanceAttributes attr = new com.heal.configuration.pojos.InstanceAttributes();
                attr.setAttributeId(rs.getInt("attributeId"));
                attr.setAttributeName(rs.getString("attributeName"));
                attr.setAttributeValue(rs.getString("attributeValue"));
                attr.setCompInstanceId(rs.getInt("compInstanceId"));
                attr.setIsUiVisible(rs.getInt("isUiVisible"));
                attr.setAttributeType(rs.getString("attributeType"));
                attr.setStatus(rs.getInt("status"));
                attr.setIsCustom(rs.getInt("isCustom"));
                return attr;
            }, accountId, compInstanceId);
        } catch (Exception e) {
            log.error("Error getting instance attribute details for accountId: {}, compInstanceId: {}", accountId, compInstanceId, e);
            return new ArrayList<>();
        }
    }

    /**
     * Gets host instance details
     * @param hostCompTypeId Host component type ID
     * @param hostAddress Host address
     * @param accountId Account ID
     * @param isDR Environment/DR flag
     * @return List of HostInstanceDetails
     */
    public List<HostInstanceDetails> getHostInstanceId(int hostCompTypeId, String hostAddress, int accountId, int isDR) {
        String sql = "SELECT DISTINCT ci1.id AS hostInstanceId, ci1.name AS hostInstanceName, ci1.is_DR AS isDR " +
                "FROM component_cluster_mapping ccm, comp_instance ci1, comp_instance ci2 " +
                "WHERE ci1.status = 1 AND ccm.comp_instance_id = ci1.id " +
                "AND ccm.cluster_id = ci2.id AND ci2.is_cluster = 1 AND ci1.is_cluster = 0 " +
                "AND ci1.mst_component_type_id = ? AND ci1.host_address = ? AND ci1.account_id = ? AND ci1.is_DR = ?";

        try {
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(HostInstanceDetails.class),
                    hostCompTypeId, hostAddress, accountId, isDR);
        } catch (Exception e) {
            log.error("Error getting host instance details: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Gets cluster instance mapping.
     * @param instanceId Component instance ID
     * @return ClusterInstancePojo
     */
    public ClusterInstancePojo getClusterInstanceMapping(int instanceId) {
        String sql = "SELECT ccm.cluster_id AS clusterId, c.identifier AS clusterIdentifier, c.name AS clusterName, " +
                "c.status AS status, c.host_id AS hostId, c.is_DR AS isDR, c.is_cluster AS isCluster, " +
                "c.mst_component_version_id AS componentVersionId, c.created_time AS createdTime, " +
                "c.updated_time AS updatedTime, c.user_details_id AS lastModifiedBy, c.account_id AS accountId, " +
                "c.mst_component_id AS componentId, c.mst_component_type_id AS componentTypeId, " +
                "c.discovery AS discovery, c.host_address AS hostAddress, c.mst_common_version_id AS commonVersionId, " +
                "c.parent_instance_id AS parentInstanceId, c.supervisor_id AS supervisorId " +
                "FROM component_cluster_mapping ccm, comp_instance c " +
                "WHERE ccm.cluster_id = c.id AND ccm.comp_instance_id = ?";

        try {
            List<ClusterInstancePojo> results = jdbcTemplate.query(sql,
                    new BeanPropertyRowMapper<>(ClusterInstancePojo.class), instanceId);
            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error getting cluster instance mapping: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * @param instanceId Component instance ID
     * @return List of CompInstKpiMapping
     * @throws HealControlCenterException if error occurs during database operation
     */
    public List<CompInstKpiMapping> getCompInstKpiMapping(int instanceId) throws HealControlCenterException {
        String sql = "SELECT compInstanceKpiId AS compInstKpiId, kpiid AS mstKpiId, " +
                     "mst_producer_kpi_mapping_id AS mstProducerKpiMappingId, " +
                     "collection_interval AS collectionInterval, status, " +
                     "mst_producer_id AS mstProducerId, notification " +
                     "FROM view_comp_instance_kpis WHERE instance_id = ?";

        try {
            log.debug("Fetching component instance KPI mapping for instanceId [{}]", instanceId);

            List<CompInstKpiMapping> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                CompInstKpiMapping mapping = new CompInstKpiMapping();
                mapping.setCompInstKpiId(rs.getInt("compInstKpiId"));
                mapping.setMstKpiId(rs.getInt("mstKpiId"));
                mapping.setMstProducerKpiMappingId(rs.getInt("mstProducerKpiMappingId"));
                mapping.setCollectionInterval(rs.getInt("collectionInterval"));
                mapping.setStatus(rs.getInt("status"));
                mapping.setMstProducerId(rs.getInt("mstProducerId"));
                mapping.setNotification(rs.getInt("notification"));
                return mapping;
            }, instanceId);

            log.debug("Successfully fetched {} component instance KPI mappings for instanceId [{}]", results.size(), instanceId);
            return results;

        } catch (Exception e) {
            log.error("Error while fetching component instance kpi mapping details for instanceId [{}]", instanceId, e);
            throw new HealControlCenterException("Error while fetching component instance kpi mapping details for instanceId [" + instanceId + "]");
        }
    }
}
