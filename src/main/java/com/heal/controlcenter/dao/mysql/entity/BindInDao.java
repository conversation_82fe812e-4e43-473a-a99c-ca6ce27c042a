package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.GroupKpiAttributeMapping;
import com.heal.controlcenter.pojo.HostInstanceDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * This implementation uses Spring JDBC with JdbcTemplate for database operations.
 */
@Slf4j
@Repository
public class BindInDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Gets group KPI attribute mapping
     * @param accountId Account ID used to filter by account
     * @param instanceId Component instance ID
     * @return List of GroupKpiAttributeMapping
     * @throws HealControlCenterException if error occurs during database operation
     */
    public List<GroupKpiAttributeMapping> getGroupKpiAttributeMapping(int accountId, int instanceId) throws HealControlCenterException {
        String sql = "SELECT ckgd.attribute_value AS attributeValue, ckgd.mst_kpi_details_id AS kpiId, " +
                     "IFNULL(ckgd.alias_name, ckgd.attribute_value) AS aliasName " +
                     "FROM comp_instance_kpi_group_details ckgd " +
                     "JOIN comp_instance ci ON ci.id = ckgd.comp_instance_id " +
                     "WHERE ci.account_id = ? AND ckgd.comp_instance_id = ?";

        try {
            log.debug("Fetching group kpi attribute mapping details for accountId [{}] and instanceId [{}]", accountId, instanceId);

            List<GroupKpiAttributeMapping> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                GroupKpiAttributeMapping mapping = new GroupKpiAttributeMapping();
                mapping.setAttributeValue(rs.getString("attributeValue"));
                mapping.setKpiId(rs.getInt("kpiId"));
                mapping.setAliasName(rs.getString("aliasName"));
                return mapping;
            }, accountId, instanceId);

            log.debug("Successfully fetched {} group kpi attribute mapping details for accountId [{}] and instanceId [{}]",
                     results.size(), accountId, instanceId);
            return results;

        } catch (Exception e) {
            log.error("Error while fetching group kpi attribute mapping details for accountId [{}] and instanceId [{}]",
                     accountId, instanceId, e);
            throw new HealControlCenterException("Error while fetching group kpi attribute mapping details for accountId [" +
                                               accountId + "] and instanceId [" + instanceId + "]");
        }
    }

    /**
     * Gets host instance details using JDBC with JOIN syntax
     * @param hostAddress Host address
     * @param accId Account ID
     * @param isDR Environment/DR flag
     * @return List of HostInstanceDetails
     * @throws HealControlCenterException if error occurs during database operation
     */
    public List<HostInstanceDetails> getHostInstanceId(String hostAddress, int accId, int isDR) throws HealControlCenterException {
        String sql = "SELECT DISTINCT ci1.id AS hostInstanceId, ci1.name AS hostInstanceName, ci1.is_DR AS isDR " +
                     "FROM component_cluster_mapping ccm " +
                     "JOIN comp_instance ci1 ON ccm.comp_instance_id = ci1.id " +
                     "JOIN comp_instance ci2 ON ccm.cluster_id = ci2.id " +
                     "WHERE ci1.status = 1 " +
                     "AND ci1.mst_component_type_id = 1 " +
                     "AND ci2.is_cluster = 1 " +
                     "AND ci1.is_cluster = 0 " +
                     "AND ci1.host_address = ? " +
                     "AND ci1.account_id = ? " +
                     "AND ci1.is_DR = ?";

        try {
            log.debug("Fetching host instance details for hostAddress [{}], accId [{}], isDR [{}]",
                     hostAddress, accId, isDR);

            List<HostInstanceDetails> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                HostInstanceDetails details = new HostInstanceDetails();
                details.setHostInstanceId(rs.getInt("hostInstanceId"));
                details.setHostInstanceName(rs.getString("hostInstanceName"));
                details.setIsDR(rs.getInt("isDR"));
                return details;
            }, hostAddress, accId, isDR);

            log.debug("Successfully fetched {} host instance details for hostAddress [{}], accId [{}], isDR [{}]",
                     results.size(), hostAddress, accId, isDR);
            return results;

        } catch (Exception e) {
            log.error("Error while fetching host instance details for hostAddress [{}], accId [{}], isDR [{}]",
                     hostAddress, accId, isDR, e);
            throw new HealControlCenterException("Error while fetching host instance details for hostAddress [" + hostAddress + "], accId [" + accId + "], isDR [" + isDR + "]");
        }
    }

    /**
     * Retrieves distinct host cluster IDs from view_cluster_services table.
     *
     * @param serviceId the list of service IDs to filter by
     * @param componentTypeId the component type ID
     * @return List of distinct cluster IDs, or empty list if none found
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public List<Integer> getHostClusterId(List<Integer> serviceId, int componentTypeId) throws HealControlCenterException {
        if (serviceId == null || serviceId.isEmpty()) {
            log.warn("Empty or null service ID list provided for getHostClusterId");
            return List.of();
        }

        // Build the IN clause dynamically
        String inClause = String.join(",", serviceId.stream().map(id -> "?").toArray(String[]::new));
        String query = "SELECT DISTINCT id FROM view_cluster_services WHERE service_id IN (" + inClause + ") AND mst_component_type_id = ?";

        try {
            log.debug("Querying view_cluster_services for host cluster IDs with serviceIds: {} and componentTypeId: {}",
                    serviceId, componentTypeId);

            // Create parameters list: all service IDs + componentTypeId at the end
            List<Object> paramsList = new ArrayList<>(serviceId);
            paramsList.add(componentTypeId);

            return jdbcTemplate.queryForList(query, Integer.class, paramsList.toArray());

        } catch (Exception e) {
            log.error("Error occurred while fetching host cluster IDs for serviceIds [{}], componentTypeId [{}]. Details: ",
                    serviceId, componentTypeId, e);
            throw new HealControlCenterException("Error occurred while fetching host cluster IDs.");
        }
    }
}
