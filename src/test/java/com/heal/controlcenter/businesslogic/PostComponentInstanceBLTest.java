package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.ComponentInstanceBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.dao.mysql.entity.*;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.AgentRepo;
import com.heal.controlcenter.dao.redis.InstanceRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ComponentInstancePojo;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for PostComponentInstanceBL.
 */
@ExtendWith(MockitoExtension.class)
class PostComponentInstanceBLTest {

    @Mock
    private ClientValidationUtils clientValidationUtils;
    @Mock
    private ServerValidationUtils serverValidationUtils;
    @Mock
    private ComponentInstanceDao componentInstanceDao;
    @Mock
    private CompInstanceDao compInstanceDao;
    @Mock
    private AccountsDao accountsDao;
    @Mock
    private AccountServiceDao accountServiceDao;
    @Mock
    private MasterDataDao masterDataDao;
    @Mock
    private MasterComponentDao masterComponentDao;
    @Mock
    private AgentDao agentDao;
    @Mock
    private EnvironmentDao environmentDao;
    @Mock
    private AccountRepo accountRepo;
    @Mock
    private ServiceRepo serviceRepo;
    @Mock
    private InstanceRepo instanceRepo;
    @Mock
    private TagMappingBL tagMappingBL;
    @Mock
    private ComponentDao componentDao;
    @Mock
    private BindInDao bindInDao;
    @Mock
    private AgentRepo agentRepo;
    @Mock
    private TagsDao tagsDao;

    private PostComponentInstanceBL postComponentInstanceBL;

    @BeforeEach
    void setUp() {
        postComponentInstanceBL = new PostComponentInstanceBL(
                clientValidationUtils,
                serverValidationUtils,
                componentInstanceDao,
                compInstanceDao,
                accountServiceDao,
                masterDataDao,
                masterComponentDao,
                agentDao,
                environmentDao,
                serviceRepo,
                instanceRepo,
                tagMappingBL,
                componentDao,
                bindInDao,
                tagsDao
        );
    }

    @Test
    void testClientValidation_Success() throws Exception {
        // Arrange
        String accountIdentifier = "test_account";
        ComponentInstancePojo request = new ComponentInstancePojo();
        request.setName("Test Instance");
        request.setComponentName("TestComponent");
        request.setComponentVersion("1.0.0");
        request.setServiceIdentifiers(Arrays.asList("service1"));
        List<ComponentInstancePojo> requests = Arrays.asList(request);

        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);

        // Act
        UtilityBean<List<ComponentInstancePojo>> result = postComponentInstanceBL.clientValidation(requests, accountIdentifier);

        // Assert
        assertNotNull(result);
        assertEquals(requests, result.getPojoObject());
//        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER_KEY));
        verify(clientValidationUtils).accountIdentifierValidation(accountIdentifier);
    }

    @Test
    void testClientValidation_NullRequestBody() {
        // Arrange
        String accountIdentifier = "test_account";

        // Act & Assert
        assertThrows(ClientException.class, () -> {
            postComponentInstanceBL.clientValidation(null, accountIdentifier);
        });
    }

    @Test
    void testClientValidation_EmptyRequestBody() {
        // Arrange
        String accountIdentifier = "test_account";
        List<ComponentInstancePojo> emptyRequests = Arrays.asList();

        // Act & Assert
        assertThrows(ClientException.class, () -> {
            postComponentInstanceBL.clientValidation(emptyRequests, accountIdentifier);
        });
    }

    @Test
    void testClientValidation_InvalidRequest() {
        // Arrange
        String accountIdentifier = "test_account";
        ComponentInstancePojo invalidRequest = new ComponentInstancePojo();
        invalidRequest.setName(""); // Invalid empty name
        List<ComponentInstancePojo> requests = Arrays.asList(invalidRequest);

        // Act & Assert
        assertThrows(ClientException.class, () -> {
            postComponentInstanceBL.clientValidation(requests, accountIdentifier);
        });
    }

    @Test
    void testServerValidation_Success() throws Exception {
        // Arrange
        String accountIdentifier = "test_account";
        ComponentInstancePojo request = new ComponentInstancePojo();
        request.setName("Test Instance");
        request.setComponentName("TestComponent");
        request.setComponentVersion("1.0.0");
        request.setServiceIdentifiers(Arrays.asList("service1"));

        Map<String, String> requestParams = new HashMap<>();
//        requestParams.put(Constants.ACCOUNT_IDENTIFIER_KEY, accountIdentifier);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, "test_user");

        UtilityBean<List<ComponentInstancePojo>> utilityBean = UtilityBean.<List<ComponentInstancePojo>>builder()
                .requestParams(requestParams)
                .pojoObject(Arrays.asList(request))
                .metadata(metadata)
                .build();

        Account account = new Account();
        account.setId(1);
        account.setIdentifier(accountIdentifier);

//        when(accountRepo.getAccountByIdentifier(accountIdentifier)).thenReturn(account);
        when(componentInstanceDao.componentInstanceExists(anyString(), anyInt())).thenReturn(false);

        // Act
        UtilityBean<List<ComponentInstanceBean>> result = postComponentInstanceBL.serverValidation(utilityBean);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getPojoObject());
        assertEquals(1, result.getPojoObject().size());
        assertEquals(account, result.getMetadata().get(Constants.ACCOUNT));
    }

    @Test
    void testServerValidation_AccountNotFound() {
        // Arrange
        String accountIdentifier = "invalid_account";
        Map<String, String> requestParams = new HashMap<>();
//        requestParams.put(Constants.ACCOUNT_IDENTIFIER_KEY, accountIdentifier);

        UtilityBean<List<ComponentInstancePojo>> utilityBean = UtilityBean.<List<ComponentInstancePojo>>builder()
                .requestParams(requestParams)
                .pojoObject(Arrays.asList())
                .metadata(new HashMap<>())
                .build();

//        when(accountRepo.getAccountByIdentifier(accountIdentifier)).thenReturn(null);

        // Act & Assert
        assertThrows(ServerException.class, () -> {
            postComponentInstanceBL.serverValidation(utilityBean);
        });
    }

    @Test
    void testProcess_Success() throws Exception {
        // Arrange
        ComponentInstanceBean componentInstance = ComponentInstanceBean.builder()
                .name("Test Instance")
                .identifier("test-instance-id")
                .accountId(1)
                .build();

        Account account = new Account();
        account.setId(1);
        account.setIdentifier("test_account");

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.ACCOUNT, account);

        UtilityBean<List<ComponentInstanceBean>> utilityBean = UtilityBean.<List<ComponentInstanceBean>>builder()
                .pojoObject(Arrays.asList(componentInstance))
                .metadata(metadata)
                .build();

        when(componentInstanceDao.insertComponentInstance(any(ComponentInstanceBean.class))).thenReturn(1);
        when(componentInstanceDao.insertInstanceMetadata(anyInt(), anyString(), anyString(), anyString())).thenReturn(1);

        // Act
        List<IdPojo> result = postComponentInstanceBL.process(utilityBean);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getId());
        assertEquals("Test Instance", result.get(0).getName());
        assertEquals("test-instance-id", result.get(0).getIdentifier());

        verify(componentInstanceDao).insertComponentInstance(any(ComponentInstanceBean.class));
        verify(componentInstanceDao).insertInstanceMetadata(anyInt(), anyString(), anyString(), anyString());
    }
}
